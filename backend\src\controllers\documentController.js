const multer = require('multer');
const { query } = require('../models/database');
const { saveFile, generateFileName, readFile, decryptData, encryptData } = require('../services/encryptionService');
const { embedSignatureInPDF } = require('../services/pdfService');
const { analyzeDocumentLayout, detectTextDirection } = require('../services/rtlTextService');
const { createMemoryEfficientUpload, trackUploadProgress, monitorMemoryUsage } = require('../middleware/streamingUpload');
const {
  logDocumentUpload,
  logDocumentSigning,
  logDocumentDownload,
  logDocumentView,
  logAccessDenied,
  extractUserContext
} = require('../services/auditService');
const { sendDocumentSignedNotification } = require('../services/notificationService');
const {
  sanitizeInput,
  sanitizeFilename,
  sanitizeNumeric,
  validateUUID
} = require('../utils/validation');

// PDF validation function
const validatePDFFile = (file, strict = false) => {
  console.log('PDF Validation - Input:', {
    hasFile: !!file,
    hasBuffer: !!(file && file.buffer),
    fileSize: file ? file.size : 'N/A',
    mimetype: file ? file.mimetype : 'N/A',
    bufferLength: file && file.buffer ? file.buffer.length : 'N/A',
    strict
  });

  if (!file || !file.buffer) {
    console.log('PDF Validation Failed: No file or buffer');
    return false;
  }

  // Check file size (minimum sanity check only)
  if (file.size < 100) { // Only check for empty/corrupted files
    console.log('PDF Validation Failed: File too small (likely empty or corrupted)', file.size);
    return false;
  }

  // Check MIME type first (this is the most common issue)
  if (file.mimetype !== 'application/pdf') {
    console.log('PDF Validation Failed: Invalid MIME type', file.mimetype);
    return false;
  }

  // In non-strict mode, just check MIME type and basic size
  if (!strict) {
    console.log('PDF Validation Passed: Non-strict mode');
    return true;
  }

  // Strict validation below
  // Check PDF header - PDF files should start with %PDF-
  const pdfHeader = file.buffer.slice(0, 5).toString('ascii');
  if (!pdfHeader.startsWith('%PDF-')) {
    console.log('PDF Validation Failed: Invalid PDF header', pdfHeader);
    return false;
  }

  // Check for PDF version (should be 1.0 to 2.0)
  const versionMatch = file.buffer.slice(0, 20).toString('ascii').match(/%PDF-(\d+\.\d+)/);
  if (!versionMatch) {
    console.log('PDF Validation Failed: No version found');
    return false;
  }

  const version = parseFloat(versionMatch[1]);
  if (version < 1.0 || version > 2.0) {
    console.log('PDF Validation Failed: Invalid version', version);
    return false;
  }

  console.log('PDF Validation Passed: Strict mode');
  return true;
};

// Get PDF file information for debugging
const getPDFInfo = (file) => {
  if (!file || !file.buffer) {
    return { valid: false, reason: 'No file or buffer provided' };
  }

  const info = {
    filename: file.originalname,
    size: file.size,
    mimetype: file.mimetype,
    bufferLength: file.buffer.length
  };

  // Check header
  const header = file.buffer.slice(0, 20).toString('ascii');
  info.header = header;
  info.startsWithPDF = header.startsWith('%PDF-');

  // Extract version if possible
  const versionMatch = header.match(/%PDF-(\d+\.\d+)/);
  if (versionMatch) {
    info.pdfVersion = versionMatch[1];
  }

  return info;
};

// Configure memory-efficient upload for large documents
const upload = createMemoryEfficientUpload({
  maxFiles: 1,
  allowedMimeTypes: ['application/pdf'],
  allowedExtensions: ['.pdf'],
  fieldSize: 100 * 1024 * 1024 // 100MB for form fields
});

// Test endpoint for PDF validation
const testPDFUpload = async (req, res) => {
  try {
    const file = req.file;

    if (!file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }

    const pdfInfo = getPDFInfo(file);
    const isValid = validatePDFFile(file, false);

    res.json({
      success: true,
      message: 'PDF test completed',
      fileInfo: pdfInfo,
      validation: {
        isValid,
        nonStrictMode: validatePDFFile(file, false),
        strictMode: validatePDFFile(file, true)
      }
    });
  } catch (error) {
    console.error('PDF test error:', error);
    res.status(500).json({
      success: false,
      message: 'PDF test failed',
      error: error.message
    });
  }
};

const signDocument = async (req, res) => {
  const startTime = Date.now();

  try {
    const { userId } = req.user;
    const file = req.file;
    let { signatureId, coordinates } = req.body;

    // Sanitize and validate inputs
    signatureId = sanitizeInput(signatureId, { maxLength: 100 });
    coordinates = sanitizeInput(coordinates, { maxLength: 200 });

    if (!file) {
      return res.status(400).json({
        success: false,
        message: 'لم يتم رفع أي ملف PDF'
      });
    }

    if (!signatureId || !validateUUID(signatureId)) {
      return res.status(400).json({
        success: false,
        message: 'معرف التوقيع مطلوب ويجب أن يكون صالحاً'
      });
    }

    // Sanitize filename
    const sanitizedFilename = sanitizeFilename(file.originalname || 'document.pdf');

    console.log('Processing document signing:', {
      userId,
      fileName: sanitizedFilename,
      fileSize: file.size,
      signatureId
    });

    // Debug: Log file information
    const pdfInfo = getPDFInfo(file);
    console.log('Uploaded file info:', pdfInfo);

    // Validate PDF file (use non-strict mode for better compatibility)
    if (!validatePDFFile(file, false)) {
      console.log('PDF validation failed:', pdfInfo);

      // Determine specific error message based on what failed
      let specificMessage = 'ملف PDF غير صالح. يرجى التأكد من أن الملف هو مستند PDF صالح.';

      if (!file.buffer) {
        specificMessage = 'لم يتم تحميل محتوى الملف بشكل صحيح. يرجى المحاولة مرة أخرى.';
      } else if (file.mimetype !== 'application/pdf') {
        specificMessage = `نوع الملف غير صحيح: ${file.mimetype}. يرجى اختيار ملف PDF فقط.`;
      } else if (file.size < 100) {
        specificMessage = 'الملف صغير جداً. يرجى التأكد من أن الملف ليس تالفاً.';
      }

      return res.status(400).json({
        success: false,
        message: specificMessage,
        details: {
          filename: pdfInfo.filename,
          size: pdfInfo.size,
          mimetype: pdfInfo.mimetype,
          startsWithPDF: pdfInfo.startsWithPDF,
          pdfVersion: pdfInfo.pdfVersion,
          header: pdfInfo.header,
          troubleshooting: [
            'تأكد من أن الملف هو مستند PDF صالح',
            'جرب فتح الملف في عارض PDF أولاً',
            'تحقق من أن الملف غير تالف',
            'تأكد من أنك تختار ملف .pdf',
            'جرب ملف PDF آخر للتأكد من عمل النظام'
          ]
        }
      });
    }

    // Parse and validate coordinates if provided
    let signatureCoordinates = { x: 50, y: 50 };
    if (coordinates) {
      try {
        const parsed = JSON.parse(coordinates);
        // Validate and sanitize coordinate values
        const x = sanitizeNumeric(parsed.x);
        const y = sanitizeNumeric(parsed.y);

        if (x !== null && y !== null && x >= 0 && x <= 1000 && y >= 0 && y <= 1000) {
          signatureCoordinates = { x, y };
        } else {
          console.warn('إحداثيات غير صالحة، استخدام القيم الافتراضية');
        }
      } catch (error) {
        console.warn('تنسيق الإحداثيات غير صالح، استخدام القيم الافتراضية');
      }
    }

    // Always use RTL and Arabic settings for Arabic-only system
    const documentAnalysis = {
      direction: 'rtl',
      hasArabicContent: true,
      language: 'ar'
    };

    // Use RTL-appropriate default positioning if not specified
    if (!coordinates) {
      signatureCoordinates = { x: 50, y: 50 }; // RTL positioning
    }

    // Get user's signature
    const signatureResult = await query(
      'SELECT id, filename, file_path, mime_type FROM signatures WHERE id = $1 AND user_id = $2',
      [signatureId, userId]
    );

    if (signatureResult.rows.length === 0) {
      return res.status(404).json({ error: 'Signature not found' });
    }

    const signature = signatureResult.rows[0];

    // Read and decrypt signature file
    const encryptedSignatureData = await readFile(signature.file_path);
    const decryptedSignatureBase64 = decryptData(encryptedSignatureData.toString());
    const signatureBuffer = Buffer.from(decryptedSignatureBase64, 'base64');

    // Get user information for signature block
    const userResult = await query(
      'SELECT id, email FROM users WHERE id = $1',
      [userId]
    );

    if (userResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    const user = userResult.rows[0];

    // Embed signature in PDF with Arabic support
    const pdfResult = await embedSignatureInPDF(
      file.buffer,
      signatureBuffer,
      {
        coordinates: signatureCoordinates,
        userId: user.id,
        userEmail: user.email
      }
    );

    // Generate filename for signed document
    const signedFileName = generateFileName(file.originalname, userId, 'signed_');
    
    // Save signed document
    const signedFilePath = await saveFile(pdfResult.pdfBytes, signedFileName, 'documents');

    // Save document metadata to database
    const documentResult = await query(
      `INSERT INTO documents (
        user_id, original_filename, signed_filename,
        serial_number, file_path, file_size, digital_signature,
        signature_coordinates, status, signed_date
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, CURRENT_TIMESTAMP)
      RETURNING id, signed_date`,
      [
        userId,
        file.originalname,
        signedFileName,
        pdfResult.serialNumber,
        signedFilePath,
        pdfResult.pdfBytes.length,
        pdfResult.digitalSignature,
        JSON.stringify(pdfResult.signatureCoordinates),
        'signed'
      ]
    );

    const document = documentResult.rows[0];
    const processingTime = Date.now() - startTime;

    // Log document upload and signing events
    await logDocumentUpload(userId, document.id, {
      fileName: file.originalname,
      fileSize: file.size,
      fileType: file.mimetype,
      processingTime
    }, req);

    await logDocumentSigning(userId, document.id, signatureId, {
      signatureType: 'digital',
      coordinates: signatureCoordinates,
      serialNumber: pdfResult.serialNumber,
      processingTime
    }, req);

    // Send WhatsApp notification (non-blocking)
    // This runs asynchronously and won't affect the response to the client
    setImmediate(async () => {
      try {
        console.log('🔔 Sending document signed notification...');

        // Get user data for notification
        const userResult = await query(
          'SELECT id, email, full_name FROM users WHERE id = $1',
          [userId]
        );

        if (userResult.rows.length > 0) {
          const userData = userResult.rows[0];

          // Prepare document data for notification
          const documentData = {
            id: document.id,
            originalFilename: file.originalname,
            signedFilename: signedFileName,
            serialNumber: pdfResult.serialNumber,
            signedAt: document.signed_at,
            createdAt: document.created_at,
            fileSize: pdfResult.pdfBytes.length
          };

          const notificationResult = await sendDocumentSignedNotification(documentData, userData);

          if (notificationResult.success) {
            console.log(`✅ WhatsApp notification sent successfully to ${notificationResult.totalSent} recipients`);
          } else {
            console.log(`⚠ WhatsApp notification failed: ${notificationResult.reason || notificationResult.error}`);
          }
        }
      } catch (notificationError) {
        // Log error but don't affect the main response
        console.error('WhatsApp notification error (non-critical):', notificationError);
      }
    });

    res.status(201).json({
      success: true,
      message: 'تم توقيع المستند بنجاح',
      document: {
        id: document.id,
        originalFilename: file.originalname,
        signedFilename: signedFileName,
        serialNumber: pdfResult.serialNumber,
        signedAt: document.signed_date,
        filePath: signedFilePath,
        fileSize: pdfResult.pdfBytes.length,
        signatureCoordinates: pdfResult.signatureCoordinates,
        userId: userId,
        metadata: {
          textDirection: 'rtl',
          language: 'ar',
          hasArabicContent: true,
          processingTime: processingTime
        }
      }
    });
  } catch (error) {
    console.error('Document signing error:', error);

    // Handle specific error types with Arabic messages
    if (error.message.includes('Only PDF files are allowed')) {
      return res.status(400).json({
        success: false,
        message: 'يُسمح فقط بملفات PDF'
      });
    }

    if (error.message.includes('Invalid PDF file') ||
        error.message.includes('Missing PDF header') ||
        error.message.includes('Failed to load PDF document')) {
      return res.status(400).json({
        success: false,
        message: 'ملف PDF غير صالح. يرجى التأكد من رفع مستند PDF صالح وغير تالف.',
        details: error.message
      });
    }

    if (error.message.includes('Unsupported signature image format')) {
      return res.status(400).json({
        success: false,
        message: 'تنسيق التوقيع غير صالح. يرجى التأكد من أن التوقيع بتنسيق PNG أو JPG.',
        details: error.message
      });
    }

    if (error.message.includes('Signature not found')) {
      return res.status(404).json({
        success: false,
        message: 'التوقيع المحدد غير موجود. يرجى اختيار توقيع صالح.'
      });
    }

    // Generic error for unexpected issues
    res.status(500).json({
      success: false,
      message: 'فشل في توقيع المستند. يرجى المحاولة مرة أخرى أو الاتصال بالدعم إذا استمرت المشكلة.',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

const getDocuments = async (req, res) => {
  try {
    const { userId } = req.user;
    const { page = 1, limit = 10, status } = req.query;
    
    const offset = (page - 1) * limit;
    
    let whereClause = 'WHERE user_id = $1';
    let queryParams = [userId];
    
    if (status) {
      whereClause += ' AND status = $2';
      queryParams.push(status);
    }
    
    const result = await query(
      `SELECT id, original_filename, signed_filename, serial_number, 
              signed_date, file_size, status, signature_coordinates
       FROM documents 
       ${whereClause}
       ORDER BY signed_date DESC 
       LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}`,
      [...queryParams, limit, offset]
    );

    // Get total count
    const countResult = await query(
      `SELECT COUNT(*) as total FROM documents ${whereClause}`,
      queryParams
    );

    res.json({
      documents: result.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: parseInt(countResult.rows[0].total),
        totalPages: Math.ceil(countResult.rows[0].total / limit)
      }
    });
  } catch (error) {
    console.error('Get documents error:', error);
    res.status(500).json({ error: 'Failed to retrieve documents' });
  }
};

const getDocument = async (req, res) => {
  try {
    const { userId } = req.user;
    const { documentId } = req.params;
    
    const result = await query(
      `SELECT id, original_filename, signed_filename, serial_number, 
              signed_date, file_path, file_size, digital_signature, 
              signature_coordinates, status
       FROM documents 
       WHERE id = $1 AND user_id = $2`,
      [documentId, userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Document not found' });
    }

    const document = result.rows[0];

    // Log document view activity
    await logDocumentView(userId, documentId, {
      viewType: 'api',
      fileName: document.original_filename
    }, req);

    res.json({
      document: document
    });
  } catch (error) {
    console.error('Get document error:', error);
    res.status(500).json({ error: 'Failed to retrieve document' });
  }
};

const downloadDocument = async (req, res) => {
  try {
    const { userId } = req.user;
    const { documentId } = req.params;

    // Validate document ID format
    if (!documentId || typeof documentId !== 'string') {
      return res.status(400).json({ error: 'Invalid document ID' });
    }

    // Get document information
    const result = await query(
      'SELECT signed_filename, file_path, file_size, original_filename FROM documents WHERE id = $1 AND user_id = $2',
      [documentId, userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Document not found or access denied' });
    }

    const document = result.rows[0];

    // Check if file exists
    let fileBuffer;
    try {
      fileBuffer = await readFile(document.file_path);
    } catch (fileError) {
      console.error('File read error:', fileError);
      return res.status(404).json({ error: 'Document file not found on server' });
    }

    // Validate file buffer
    if (!fileBuffer || fileBuffer.length === 0) {
      return res.status(500).json({ error: 'Document file is empty or corrupted' });
    }

    // Sanitize filename for download
    const safeFilename = document.signed_filename.replace(/[^a-zA-Z0-9._-]/g, '_');

    // Log download activity using audit service
    await logDocumentDownload(userId, documentId, {
      fileName: document.signed_filename,
      originalFileName: document.original_filename,
      fileSize: document.file_size,
      downloadType: 'signed'
    }, req);

    // Set appropriate headers
    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="${safeFilename}"`,
      'Content-Length': fileBuffer.length,
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    });

    res.send(fileBuffer);
  } catch (error) {
    console.error('Download document error:', error);

    // Handle specific error types
    if (error.message.includes('ENOENT')) {
      return res.status(404).json({ error: 'Document file not found on server' });
    }

    res.status(500).json({
      error: 'Failed to download document',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// View document endpoint for PDF viewer
const viewDocument = async (req, res) => {
  try {
    const userId = req.user?.id || req.user?.userId;
    const { documentId } = req.params;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'المصادقة مطلوبة',
        code: 'AUTHENTICATION_REQUIRED'
      });
    }

    // Get document information with access check
    const result = await query(
      `SELECT id, original_filename, signed_filename, file_path, file_size, status
       FROM documents
       WHERE id = $1 AND user_id = $2`,
      [documentId, userId]
    );

    if (result.rows.length === 0) {
      await logAccessDenied(userId, documentId, 'Document not found or access denied', req);
      return res.status(404).json({
        success: false,
        error: 'المستند غير موجود أو ليس لديك صلاحية للوصول إليه',
        code: 'DOCUMENT_NOT_FOUND'
      });
    }

    const document = result.rows[0];

    // Check if document is signed (only allow viewing of signed documents)
    if (document.status !== 'signed') {
      return res.status(400).json({
        success: false,
        error: 'يمكن عرض المستندات الموقعة فقط',
        code: 'DOCUMENT_NOT_SIGNED'
      });
    }

    // Read the PDF file
    let fileBuffer;
    try {
      fileBuffer = await readFile(document.file_path);
    } catch (fileError) {
      console.error('File read error:', fileError);
      return res.status(404).json({
        success: false,
        error: 'ملف المستند غير موجود على الخادم',
        code: 'FILE_NOT_FOUND'
      });
    }

    // Validate file buffer
    if (!fileBuffer || fileBuffer.length === 0) {
      return res.status(500).json({
        success: false,
        error: 'ملف المستند فارغ أو تالف',
        code: 'FILE_CORRUPTED'
      });
    }

    // Log document view activity
    await logDocumentView(userId, documentId, {
      viewType: 'browser',
      fileName: document.signed_filename || document.original_filename,
      fileSize: document.file_size
    }, req);

    // Set appropriate headers for PDF viewing
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Length', fileBuffer.length);
    res.setHeader('Content-Disposition', 'inline; filename="' + encodeURIComponent(document.signed_filename || document.original_filename) + '"');
    res.setHeader('Cache-Control', 'private, no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    // Enable CORS for PDF.js
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    // Send the PDF file
    res.send(fileBuffer);

  } catch (error) {
    console.error('View document error:', error);
    res.status(500).json({
      success: false,
      error: 'فشل في عرض المستند',
      code: 'VIEW_DOCUMENT_ERROR',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Verify document by serial number
const verifyDocumentBySerial = async (req, res) => {
  try {
    const { serialNumber } = req.params;

    if (!serialNumber) {
      return res.status(400).json({
        success: false,
        message: 'الرقم التسلسلي مطلوب'
      });
    }

    console.log('Verifying document with serial number:', serialNumber);

    // Query document with user information
    const result = await query(
      `SELECT
        d.id,
        d.original_filename,
        d.signed_filename,
        d.serial_number,
        d.signed_date,
        d.file_size,
        d.digital_signature,
        d.signature_coordinates,
        d.user_id,
        u.email as user_email
      FROM documents d
      JOIN users u ON d.user_id = u.id
      WHERE d.serial_number = $1 AND d.status = 'signed'`,
      [serialNumber]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        isValid: false,
        error: 'الرقم التسلسلي غير موجود في النظام'
      });
    }

    const document = result.rows[0];

    // Log verification attempt
    await query(
      'INSERT INTO logs (user_id, action, details) VALUES ($1, $2, $3)',
      [req.user?.userId || null, 'DOCUMENT_VERIFICATION', {
        serialNumber: serialNumber,
        documentId: document.id,
        verificationTime: new Date(),
        verifierIP: req.ip
      }]
    );

    res.json({
      success: true,
      isValid: true,
      document: {
        id: document.id,
        original_filename: document.original_filename,
        signed_filename: document.signed_filename,
        serial_number: document.serial_number,
        signed_date: document.signed_date,
        file_size: document.file_size,
        user_id: document.user_id,
        user_email: document.user_email,
        digital_signature: document.digital_signature,
        signature_coordinates: document.signature_coordinates
      }
    });

  } catch (error) {
    console.error('Document verification error:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في التحقق من الرقم التسلسلي'
    });
  }
};

module.exports = {
  upload: upload.single('document'),
  uploadWithTracking: [trackUploadProgress, monitorMemoryUsage, upload.single('document')],
  testPDFUpload,
  signDocument,
  getDocuments,
  getDocument,
  downloadDocument,
  viewDocument,
  verifyDocumentBySerial
};
