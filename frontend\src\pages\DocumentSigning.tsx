import React, { useState, useRef, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { signatureAPI } from '../services/api';
import { useLanguage } from '../contexts/LanguageContext';
import { useAuth } from '../services/AuthContext';
import UploadProgress from '../components/UploadProgress';
import useUploadProgress from '../hooks/useUploadProgress';

interface Signature {
  id: string;
  filename: string;
  uploadDate: string;
}

const DocumentSigning: React.FC = () => {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const { isAdmin } = useAuth();
  const { progress, uploadFile, resetProgress } = useUploadProgress();

  // Debug log to verify isAdmin is available
  console.log('DocumentSigning - isAdmin function:', typeof isAdmin, isAdmin);
  const [signatures, setSignatures] = useState<Signature[]>([]);
  const [selectedSignature, setSelectedSignature] = useState('');
  const [document, setDocument] = useState<File | null>(null);
  const [coordinates, setCoordinates] = useState({ x: 50, y: 50 });
  const [signing, setSigning] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    fetchSignatures();
  }, []);

  const fetchSignatures = async () => {
    try {
      console.log('Fetching signatures...');
      const response = await signatureAPI.getAll();
      console.log('Signatures response:', response.data);
      const fetchedSignatures = response.data.signatures;
      setSignatures(fetchedSignatures);

      // Automatically select the first signature if available
      if (fetchedSignatures.length > 0) {
        setSelectedSignature(fetchedSignatures[0].id);
        console.log('Auto-selected signature:', fetchedSignatures[0].id);
      } else {
        setSelectedSignature('');
        console.log('No signatures available');
      }
    } catch (error: any) {
      console.error('Error fetching signatures:', error);
      console.error('Signature fetch error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message
      });

      if (error.response?.status === 401) {
        setError('يرجى تسجيل الدخول أولاً');
      } else {
        setError('فشل في تحميل التوقيعات. يرجى المحاولة مرة أخرى.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleFileSelect = (files: FileList | null) => {
    if (!files || files.length === 0) return;

    const file = files[0];

    // Check file extension as backup for MIME type
    const fileName = file.name.toLowerCase();
    const isPdfByExtension = fileName.endsWith('.pdf');
    const isPdfByMimeType = file.type === 'application/pdf';

    // Accept if either MIME type is correct OR file extension is .pdf
    // Some browsers/systems may not set the correct MIME type
    if (!isPdfByMimeType && !isPdfByExtension) {
      setError(`${t.fileValidation.pdfOnly} (نوع الملف: ${file.type || 'غير محدد'})`);
      return;
    }

    // Check for minimum file size (empty files)
    if (file.size < 100) {
      setError('الملف صغير جداً. يرجى التأكد من أن الملف ليس فارغاً أو تالفاً.');
      return;
    }

    setDocument(file);
    setError('');

    // Auto-trigger signing process after file selection
    setTimeout(() => {
      autoSignDocument(file);
    }, 500); // Small delay to ensure state is updated
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files);
    }
  };

  const autoSignDocument = async (file: File) => {
    console.log('Auto-signing document:', file.name);

    // Check if user is admin
    if (!isAdmin()) {
      setError('عذراً، توقيع المستندات متاح للمديرين فقط. سيتم توجيهك إلى لوحة التحكم.');
      setTimeout(() => {
        navigate('/dashboard');
      }, 3000);
      return;
    }

    // Check if we have signatures available
    if (signatures.length === 0) {
      setError('يرجى رفع توقيع واحد على الأقل قبل توقيع المستندات. سيتم توجيهك إلى صفحة رفع التوقيع.');
      setTimeout(() => {
        navigate('/signature-upload');
      }, 3000);
      return;
    }

    // Auto-select the first available signature
    const autoSelectedSignature = signatures[0].id;
    setSelectedSignature(autoSelectedSignature);

    // Use default coordinates for automatic signing
    const autoCoordinates = { x: 100, y: 100 };
    setCoordinates(autoCoordinates);

    setError('');
    setSuccess('');
    setSigning(true);
    resetProgress();

    try {
      const response = await uploadFile(
        file,
        '/documents/sign',
        {
          signatureId: autoSelectedSignature,
          coordinates: JSON.stringify(autoCoordinates)
        }
      );

      const result = response.document;

      // Show brief success message
      const successMessage = `${t.success.documentSigned}! ${t.history.serialNumber}: ${result.serialNumber}`;
      setSuccess(successMessage);

      // Redirect to history page after 2 seconds
      setTimeout(() => {
        navigate('/history');
      }, 2000);

    } catch (error: any) {
      console.error('Auto-signing error:', error);
      const errorMessage = error.response?.data?.error ||
                          error.message ||
                          t.errors.signingFailed;
      setError(`فشل في توقيع المستند تلقائياً: ${errorMessage}`);
      setSigning(false);
    }
  };

  const signDocument = async () => {
    console.log('Starting document signing process...');
    console.log('Document:', document?.name);
    console.log('Selected signature:', selectedSignature);
    console.log('Coordinates:', coordinates);
    console.log('Token in localStorage:', localStorage.getItem('token') ? 'Present' : 'Missing');

    // Check if user is admin
    if (!isAdmin()) {
      setError('عذراً، توقيع المستندات متاح للمديرين فقط.');
      return;
    }

    if (!document) {
      setError(t.errors.documentMissing);
      return;
    }

    if (!selectedSignature) {
      setError('يرجى رفع توقيع واحد على الأقل قبل توقيع المستندات');
      return;
    }

    setError('');
    setSuccess('');
    setSigning(true);
    resetProgress();

    try {
      const response = await uploadFile(
        document,
        '/documents/sign',
        {
          signatureId: selectedSignature,
          coordinates: JSON.stringify(coordinates)
        }
      );

      const result = response.document;

      // Prepare document details for confirmation page
      const documentDetails = {
        id: result.id,
        originalName: document.name,
        serialNumber: result.serialNumber,
        signedAt: result.signedAt || new Date().toISOString(),
        fileSize: document.size,
        fileType: 'application/pdf',
        signedFilePath: result.filePath
      };

      // Show brief success message before redirect
      const successMessage = `${t.success.documentSigned}! ${t.history.serialNumber}: ${result.serialNumber}`;
      setSuccess(successMessage);

      // Redirect to confirmation page after 2 seconds
      setTimeout(() => {
        navigate(`/signing-confirmation/${result.id}`, {
          state: {
            documentDetails,
            successMessage
          }
        });
      }, 2000);

    } catch (error: any) {
      console.error('Signing error:', error);
      console.error('Error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message,
        selectedSignature,
        documentName: document?.name,
        coordinates
      });

      // Prepare error details with better user messages
      let userMessage = t.errors.signingFailed;

      if (error.response?.status === 401) {
        userMessage = 'انتهت جلسة المستخدم. يرجى تسجيل الدخول مرة أخرى.';
      } else if (error.response?.status === 404) {
        userMessage = 'التوقيع المحدد غير موجود. يرجى اختيار توقيع آخر.';
      } else if (error.response?.status === 400) {
        userMessage = error.response?.data?.message || 'بيانات غير صحيحة. يرجى التحقق من الملف والتوقيع.';
      } else if (error.response?.status === 500) {
        userMessage = 'خطأ في الخادم. يرجى المحاولة مرة أخرى لاحقاً.';
      } else if (error.message.includes('Network Error')) {
        userMessage = 'خطأ في الاتصال. يرجى التحقق من الاتصال بالإنترنت.';
      } else if (error.response?.data?.error) {
        userMessage = error.response.data.error;
      } else if (error.response?.data?.message) {
        userMessage = error.response.data.message;
      }

      const errorDetails = {
        message: userMessage,
        code: error.response?.data?.code || 'SIGNING_ERROR',
        timestamp: new Date().toISOString(),
        retryable: true,
        originalFileName: document.name
      };

      // Redirect to error page after brief delay
      setTimeout(() => {
        navigate('/signing-error', {
          state: {
            error: errorDetails,
            fromSigning: true
          }
        });
      }, 1500);

      setError(errorDetails.message);
    } finally {
      setSigning(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 بايت';
    const k = 1024;
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };





  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6" dir="rtl">
      {/* Admin-only warning for non-admin users */}
      {!isAdmin() && (
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-amber-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="mr-3">
              <h3 className="text-sm font-medium text-amber-800 font-['Almarai']">
                صفحة مخصصة للمديرين فقط
              </h3>
              <p className="mt-1 text-sm text-amber-700 font-['Almarai']">
                توقيع المستندات متاح للمديرين فقط. يرجى تسجيل الدخول بحساب مدير للوصول إلى هذه الميزة.
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="bg-white p-6 rounded-lg shadow-md">
        <h1 className="text-2xl font-bold text-gray-800 mb-4">{t.documentSigning.title}</h1>
        <p className="text-gray-600 mb-6">{t.documentSigning.subtitle}</p>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-3 sm:px-4 py-2 sm:py-3 rounded mb-3 sm:mb-4 font-['Almarai']">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <svg className="h-4 w-4 sm:h-5 sm:w-5 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="mr-2 sm:mr-3 min-w-0 flex-1">
                <p className="text-xs sm:text-sm break-words">{error}</p>
              </div>
            </div>
          </div>
        )}

        {success && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-3 sm:px-4 py-2 sm:py-3 rounded mb-3 sm:mb-4 font-['Almarai']">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <svg className="h-4 w-4 sm:h-5 sm:w-5 text-green-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="mr-2 sm:mr-3 min-w-0 flex-1">
                <p className="text-xs sm:text-sm break-words">{success}</p>
                <p className="text-xs text-green-600 mt-1">سيتم التوجيه تلقائياً...</p>
              </div>
            </div>
          </div>
        )}



        {/* Document Upload */}
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-3">١. {t.documentSigning.selectDocument}</h2>
          <div
            className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
              !isAdmin()
                ? 'border-gray-200 bg-gray-50 cursor-not-allowed'
                : dragActive
                  ? 'border-primary-500 bg-primary-50'
                  : 'border-gray-300 hover:border-gray-400'
            }`}
            onDragEnter={isAdmin() ? handleDrag : undefined}
            onDragLeave={isAdmin() ? handleDrag : undefined}
            onDragOver={isAdmin() ? handleDrag : undefined}
            onDrop={isAdmin() ? handleDrop : undefined}
          >
            {document ? (
              <div className="space-y-2">
                <div className="mx-auto w-8 h-8 text-green-500">
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <p className="font-medium text-gray-900">{document.name}</p>
                <p className="text-sm text-gray-600">{formatFileSize(document.size)}</p>
                <button
                  onClick={() => {
                    setDocument(null);
                    if (fileInputRef.current) fileInputRef.current.value = '';
                  }}
                  className="text-red-500 hover:text-red-700 text-sm"
                >
                  {t.common.delete}
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="mx-auto w-12 h-12 text-gray-400">
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div>
                  {isAdmin() ? (
                    <p className="text-lg font-medium text-gray-900">
                      اسحب مستند PDF هنا، أو{' '}
                      <button
                        onClick={() => fileInputRef.current?.click()}
                        className="text-blue-500 hover:text-blue-600"
                      >
                        {t.signatureUpload.browse}
                      </button>
                    </p>
                  ) : (
                    <p className="text-lg font-medium text-gray-500">
                      توقيع المستندات متاح للمديرين فقط
                    </p>
                  )}
                </div>
              </div>
            )}
            
            <input
              ref={fileInputRef}
              type="file"
              className="hidden"
              accept=".pdf"
              onChange={(e) => handleFileSelect(e.target.files)}
              disabled={signing || !isAdmin()}
            />
          </div>
        </div>

        {/* No Signatures Warning */}
        {signatures.length === 0 && (
          <div className="mb-6">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
              <div className="mx-auto w-12 h-12 text-yellow-500 mb-4">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-yellow-800 mb-2">لا توجد توقيعات متاحة</h3>
              <p className="text-yellow-700 mb-4">
                يجب رفع توقيع واحد على الأقل قبل البدء في توقيع المستندات
              </p>
              <Link
                to="/signature-upload"
                className="inline-block bg-yellow-500 text-white px-6 py-2 rounded-md hover:bg-yellow-600 transition-colors"
              >
                رفع التوقيع الآن
              </Link>
            </div>
          </div>
        )}

        {/* Signature Position - Hidden in auto mode */}
        {false && (
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-3">٢. {t.documentSigning.signaturePosition}</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="x-coordinate" className="block text-sm font-medium text-gray-700 mb-1">
                {t.documentSigning.xCoordinate}
              </label>
              <input
                type="number"
                id="x-coordinate"
                value={coordinates.x}
                onChange={(e) => setCoordinates({ ...coordinates, x: parseInt(e.target.value) || 0 })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                min="0"
                max="1000"
              />
            </div>
            <div>
              <label htmlFor="y-coordinate" className="block text-sm font-medium text-gray-700 mb-1">
                {t.documentSigning.yCoordinate}
              </label>
              <input
                type="number"
                id="y-coordinate"
                value={coordinates.y}
                onChange={(e) => setCoordinates({ ...coordinates, y: parseInt(e.target.value) || 0 })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                min="0"
                max="1000"
              />
            </div>
          </div>
          <p className="text-sm text-gray-600 mt-2">
            سيتم ضبط موضع التوقيع تلقائيًا للمستندات العربية من اليمين إلى اليسار
          </p>
        </div>
        )}

        {/* Sign Button - Hidden in auto mode */}
        {false && (
        <div className="text-center">
          <button
            onClick={signDocument}
            disabled={signing || !document || !selectedSignature || progress.isUploading}
            className="bg-blue-500 text-white px-8 py-3 rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {signing || progress.isUploading ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                {progress.isUploading ? 'جاري الرفع...' : t.documentSigning.signing}
              </div>
            ) : (
              t.documentSigning.signDocument
            )}
          </button>
        </div>
        )}

        {/* Auto-Processing Status */}
        {document && signing && (
          <div className="mb-4 sm:mb-6 p-3 sm:p-4 bg-blue-50 border border-blue-200 rounded-lg font-['Almarai']">
            <div className="flex flex-col sm:flex-row sm:items-center">
              <div className="flex items-center justify-center sm:justify-start mb-2 sm:mb-0">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500 ml-3"></div>
                <h3 className="text-base sm:text-lg font-semibold text-blue-800">جاري المعالجة التلقائية</h3>
              </div>
              <div className="text-center sm:text-right sm:mr-3">
                <p className="text-blue-600 text-xs sm:text-sm leading-relaxed">
                  يتم توقيع المستند تلقائياً وسيتم توجيهك إلى صفحة السجل عند الانتهاء
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Upload Progress */}
        {(progress.isUploading || progress.error || progress.percentage > 0) && (
          <div className="mt-6">
            <UploadProgress
              progress={progress}
              fileName={document?.name}
              onCancel={() => {
                resetProgress();
                setSigning(false);
              }}
            />
          </div>
        )}
      </div>


    </div>
  );
};

export default DocumentSigning;
