const fs = require('fs');
const http = require('http');
const https = require('https');

// Simple HTTP request helper
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {}
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData, headers: res.headers });
        } catch (e) {
          resolve({ status: res.statusCode, data: data, headers: res.headers });
        }
      });
    });

    req.on('error', reject);
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

// Comprehensive backend and frontend connection test
async function testComprehensiveConnection() {
  console.log('=== Comprehensive Backend and Frontend Connection Test ===\n');
  
  const results = {
    backend: { passed: 0, failed: 0, tests: [] },
    frontend: { passed: 0, failed: 0, tests: [] },
    api: { passed: 0, failed: 0, tests: [] },
    database: { passed: 0, failed: 0, tests: [] }
  };

  let authToken = null;

  try {
    // ===== BACKEND TESTS =====
    console.log('🔧 BACKEND TESTS');
    console.log('================');

    // Test 1: Health endpoint
    console.log('\n1. Health endpoint...');
    try {
      const healthResponse = await makeRequest('http://localhost:3001/health');
      if (healthResponse.status === 200) {
        console.log('✓ Health endpoint working');
        results.backend.passed++;
        results.backend.tests.push({ name: 'Health endpoint', status: 'PASS' });
      } else {
        console.log('❌ Health endpoint failed');
        results.backend.failed++;
        results.backend.tests.push({ name: 'Health endpoint', status: 'FAIL' });
      }
    } catch (error) {
      console.log('❌ Health endpoint error:', error.message);
      results.backend.failed++;
      results.backend.tests.push({ name: 'Health endpoint', status: 'ERROR' });
    }

    // Test 2: CORS headers
    console.log('\n2. CORS configuration...');
    try {
      const corsResponse = await makeRequest('http://localhost:3001/health');
      const corsHeaders = corsResponse.headers;
      if (corsHeaders['access-control-allow-origin']) {
        console.log('✓ CORS headers present');
        results.backend.passed++;
        results.backend.tests.push({ name: 'CORS headers', status: 'PASS' });
      } else {
        console.log('⚠ CORS headers missing');
        results.backend.failed++;
        results.backend.tests.push({ name: 'CORS headers', status: 'WARN' });
      }
    } catch (error) {
      console.log('❌ CORS test error:', error.message);
      results.backend.failed++;
      results.backend.tests.push({ name: 'CORS headers', status: 'ERROR' });
    }

    // ===== AUTHENTICATION TESTS =====
    console.log('\n\n🔐 AUTHENTICATION TESTS');
    console.log('=======================');

    // Test 3: User registration
    console.log('\n3. User registration...');
    try {
      const testEmail = '<EMAIL>';
      const testPassword = 'MySecureP@ssw0rd2024!Complex';
      
      const registerData = JSON.stringify({
        email: testEmail,
        password: testPassword
      });
      
      const registerResponse = await makeRequest('http://localhost:3001/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(registerData)
        },
        body: registerData
      });
      
      if (registerResponse.status === 201) {
        console.log('✓ User registration working');
        authToken = registerResponse.data.token;
        results.api.passed++;
        results.api.tests.push({ name: 'User registration', status: 'PASS' });
      } else if (registerResponse.status === 409) {
        console.log('ℹ User exists, trying login...');
        
        const loginResponse = await makeRequest('http://localhost:3001/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(registerData)
          },
          body: registerData
        });
        
        if (loginResponse.status === 200) {
          console.log('✓ User login working');
          authToken = loginResponse.data.token;
          results.api.passed++;
          results.api.tests.push({ name: 'User login', status: 'PASS' });
        } else {
          console.log('❌ Login failed');
          results.api.failed++;
          results.api.tests.push({ name: 'User login', status: 'FAIL' });
        }
      } else {
        console.log('❌ Registration failed');
        results.api.failed++;
        results.api.tests.push({ name: 'User registration', status: 'FAIL' });
      }
    } catch (error) {
      console.log('❌ Authentication error:', error.message);
      results.api.failed++;
      results.api.tests.push({ name: 'Authentication', status: 'ERROR' });
    }

    // ===== API ENDPOINT TESTS =====
    console.log('\n\n🌐 API ENDPOINT TESTS');
    console.log('====================');

    if (authToken) {
      // Test 4: Profile endpoint
      console.log('\n4. Profile endpoint...');
      try {
        const profileResponse = await makeRequest('http://localhost:3001/api/auth/profile', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${authToken}`
          }
        });
        
        if (profileResponse.status === 200) {
          console.log('✓ Profile endpoint working');
          results.api.passed++;
          results.api.tests.push({ name: 'Profile endpoint', status: 'PASS' });
        } else {
          console.log('❌ Profile endpoint failed');
          results.api.failed++;
          results.api.tests.push({ name: 'Profile endpoint', status: 'FAIL' });
        }
      } catch (error) {
        console.log('❌ Profile endpoint error:', error.message);
        results.api.failed++;
        results.api.tests.push({ name: 'Profile endpoint', status: 'ERROR' });
      }

      // Test 5: Document endpoints
      console.log('\n5. Document endpoints...');
      try {
        const documentsResponse = await makeRequest('http://localhost:3001/api/documents', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${authToken}`
          }
        });
        
        if (documentsResponse.status === 200) {
          console.log('✓ Documents endpoint working');
          results.api.passed++;
          results.api.tests.push({ name: 'Documents endpoint', status: 'PASS' });
        } else {
          console.log('❌ Documents endpoint failed');
          results.api.failed++;
          results.api.tests.push({ name: 'Documents endpoint', status: 'FAIL' });
        }
      } catch (error) {
        console.log('❌ Documents endpoint error:', error.message);
        results.api.failed++;
        results.api.tests.push({ name: 'Documents endpoint', status: 'ERROR' });
      }

      // Test 6: Signatures endpoint
      console.log('\n6. Signatures endpoint...');
      try {
        const signaturesResponse = await makeRequest('http://localhost:3001/api/signatures', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${authToken}`
          }
        });
        
        if (signaturesResponse.status === 200) {
          console.log('✓ Signatures endpoint working');
          results.api.passed++;
          results.api.tests.push({ name: 'Signatures endpoint', status: 'PASS' });
        } else {
          console.log('❌ Signatures endpoint failed');
          results.api.failed++;
          results.api.tests.push({ name: 'Signatures endpoint', status: 'FAIL' });
        }
      } catch (error) {
        console.log('❌ Signatures endpoint error:', error.message);
        results.api.failed++;
        results.api.tests.push({ name: 'Signatures endpoint', status: 'ERROR' });
      }
    }

    // ===== DATABASE TESTS =====
    console.log('\n\n🗄️ DATABASE TESTS');
    console.log('=================');

    // Test 7: Database health
    console.log('\n7. Database connectivity...');
    try {
      const dbHealthResponse = await makeRequest('http://localhost:3001/health');
      if (dbHealthResponse.status === 200 && dbHealthResponse.data.message) {
        console.log('✓ Database connectivity confirmed');
        results.database.passed++;
        results.database.tests.push({ name: 'Database connectivity', status: 'PASS' });
      } else {
        console.log('❌ Database connectivity failed');
        results.database.failed++;
        results.database.tests.push({ name: 'Database connectivity', status: 'FAIL' });
      }
    } catch (error) {
      console.log('❌ Database test error:', error.message);
      results.database.failed++;
      results.database.tests.push({ name: 'Database connectivity', status: 'ERROR' });
    }

    // ===== FRONTEND TESTS =====
    console.log('\n\n🖥️ FRONTEND TESTS');
    console.log('=================');

    // Test 8: Frontend accessibility
    console.log('\n8. Frontend accessibility...');
    try {
      const frontendResponse = await makeRequest('http://localhost:3000');
      if (frontendResponse.status === 200) {
        console.log('✓ Frontend accessible');
        results.frontend.passed++;
        results.frontend.tests.push({ name: 'Frontend accessibility', status: 'PASS' });
      } else {
        console.log('⚠ Frontend returned status:', frontendResponse.status);
        results.frontend.failed++;
        results.frontend.tests.push({ name: 'Frontend accessibility', status: 'WARN' });
      }
    } catch (error) {
      console.log('⚠ Frontend not accessible:', error.message);
      results.frontend.failed++;
      results.frontend.tests.push({ name: 'Frontend accessibility', status: 'WARN' });
    }

    // ===== SUMMARY =====
    console.log('\n\n📊 TEST RESULTS SUMMARY');
    console.log('=======================');
    
    const totalPassed = results.backend.passed + results.frontend.passed + results.api.passed + results.database.passed;
    const totalFailed = results.backend.failed + results.frontend.failed + results.api.failed + results.database.failed;
    const totalTests = totalPassed + totalFailed;
    
    console.log(`\nBackend Tests: ${results.backend.passed}/${results.backend.passed + results.backend.failed} passed`);
    console.log(`Frontend Tests: ${results.frontend.passed}/${results.frontend.passed + results.frontend.failed} passed`);
    console.log(`API Tests: ${results.api.passed}/${results.api.passed + results.api.failed} passed`);
    console.log(`Database Tests: ${results.database.passed}/${results.database.passed + results.database.failed} passed`);
    
    console.log(`\nOverall: ${totalPassed}/${totalTests} tests passed (${Math.round(totalPassed/totalTests*100)}%)`);
    
    if (totalPassed === totalTests) {
      console.log('\n🎉 ALL TESTS PASSED! Backend and frontend connections are working perfectly.');
    } else if (totalPassed >= totalTests * 0.8) {
      console.log('\n✅ Most tests passed. System is mostly functional with minor issues.');
    } else {
      console.log('\n⚠️ Several tests failed. System has significant issues that need attention.');
    }

  } catch (error) {
    console.error('❌ Comprehensive test failed:', error.message);
  }
}

testComprehensiveConnection();
