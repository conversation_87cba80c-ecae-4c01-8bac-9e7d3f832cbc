const fs = require('fs');
const http = require('http');

// Simple HTTP request helper
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    http.get(url, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => resolve(data));
    }).on('error', reject);
  });
}

// Test document upload functionality
async function testDocumentUpload() {
  try {
    console.log('Testing document upload functionality...');
    
    // Test 1: Health check
    console.log('\n1. Testing health endpoint...');
    const healthResponse = await makeRequest('http://localhost:3001/health');
    console.log('✓ Health check passed:', JSON.parse(healthResponse).message);
    
    // Test 2: Test PDF upload endpoint (without authentication)
    console.log('\n2. Testing PDF upload endpoint...');

    // Create a simple test PDF content
    const testPdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Test Document) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
299
%%EOF`;

    // Write test PDF to file
    fs.writeFileSync('test.pdf', testPdfContent);
    console.log('✓ Test PDF file created');
    
    // Test upload endpoint accessibility
    try {
      const uploadResponse = await makeRequest('http://localhost:3001/api/documents/test-pdf');
      console.log('Upload endpoint response:', uploadResponse);
    } catch (error) {
      console.log('Expected error (authentication required):', error.message);
    }
    
    // Test 3: Check if uploads directory exists
    console.log('\n3. Checking upload directory...');
    const uploadsPath = './backend/uploads';
    if (fs.existsSync(uploadsPath)) {
      console.log('✓ Uploads directory exists');
      const files = fs.readdirSync(uploadsPath);
      console.log('Files in uploads directory:', files.length);
    } else {
      console.log('⚠ Uploads directory does not exist');
      // Create uploads directory
      fs.mkdirSync(uploadsPath, { recursive: true });
      console.log('✓ Created uploads directory');
    }
    
    // Test 4: Check disk space
    console.log('\n4. Checking disk space...');
    const stats = fs.statSync('./');
    console.log('✓ File system accessible');
    
    // Clean up
    if (fs.existsSync('test.pdf')) {
      fs.unlinkSync('test.pdf');
      console.log('✓ Cleaned up test file');
    }
    
    console.log('\n=== Upload Test Summary ===');
    console.log('✓ Backend server is running');
    console.log('✓ Health endpoint working');
    console.log('✓ Upload endpoint accessible (authentication required)');
    console.log('✓ File system operations working');
    console.log('✓ Uploads directory ready');
    
  } catch (error) {
    console.error('Test failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

testDocumentUpload();
