const fs = require('fs');
const http = require('http');

// Simple HTTP request helper with detailed error reporting
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || 80,
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {}
    };

    const req = http.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ 
            status: res.statusCode, 
            data: jsonData, 
            headers: res.headers,
            rawData: data
          });
        } catch (e) {
          resolve({ 
            status: res.statusCode, 
            data: data, 
            headers: res.headers,
            rawData: data,
            parseError: e.message
          });
        }
      });
    });

    req.on('error', (error) => {
      reject({
        error: error.message,
        code: error.code,
        url: url,
        options: options
      });
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

// Detailed API diagnostics
async function runAPIdiagnostics() {
  console.log('=== API Endpoint Diagnostics ===\n');
  
  let authToken = null;

  try {
    // Step 1: Get authentication token
    console.log('1. Getting authentication token...');
    
    const testEmail = 'diagnostictest' + Date.now() + '@example.com';
    const testPassword = 'MySecureP@ssw0rd2024!Complex';
    
    const registerData = JSON.stringify({
      email: testEmail,
      password: testPassword
    });
    
    try {
      const registerResponse = await makeRequest('http://localhost:3001/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(registerData)
        },
        body: registerData
      });
      
      if (registerResponse.status === 201) {
        authToken = registerResponse.data.token;
        console.log('✓ New user registered, token obtained');
      } else if (registerResponse.status === 409) {
        // User exists, try login
        const loginResponse = await makeRequest('http://localhost:3001/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(registerData)
          },
          body: registerData
        });
        
        if (loginResponse.status === 200) {
          authToken = loginResponse.data.token;
          console.log('✓ Existing user logged in, token obtained');
        } else {
          console.log('❌ Login failed:', loginResponse.data);
          console.log('Response details:', JSON.stringify(loginResponse, null, 2));
        }
      } else {
        console.log('❌ Registration failed:', registerResponse.data);
        console.log('Response details:', JSON.stringify(registerResponse, null, 2));
      }
    } catch (error) {
      console.log('❌ Authentication request failed:', error);
    }

    if (!authToken) {
      console.log('❌ Could not obtain authentication token. Stopping diagnostics.');
      return;
    }

    console.log('Token preview:', authToken.substring(0, 20) + '...');

    // Step 2: Test profile endpoint with detailed debugging
    console.log('\n2. Testing profile endpoint...');
    try {
      const profileResponse = await makeRequest('http://localhost:3001/api/auth/profile', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('Profile response status:', profileResponse.status);
      console.log('Profile response data:', JSON.stringify(profileResponse.data, null, 2));
      
      if (profileResponse.status === 200) {
        console.log('✓ Profile endpoint working correctly');
      } else {
        console.log('❌ Profile endpoint failed');
        console.log('Headers sent:', {
          'Authorization': `Bearer ${authToken.substring(0, 20)}...`,
          'Content-Type': 'application/json'
        });
      }
    } catch (error) {
      console.log('❌ Profile request error:', error);
    }

    // Step 3: Test documents endpoint
    console.log('\n3. Testing documents endpoint...');
    try {
      const documentsResponse = await makeRequest('http://localhost:3001/api/documents', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('Documents response status:', documentsResponse.status);
      console.log('Documents response data:', JSON.stringify(documentsResponse.data, null, 2));
      
      if (documentsResponse.status === 200) {
        console.log('✓ Documents endpoint working correctly');
      } else {
        console.log('❌ Documents endpoint failed');
      }
    } catch (error) {
      console.log('❌ Documents request error:', error);
    }

    // Step 4: Test signatures endpoint
    console.log('\n4. Testing signatures endpoint...');
    try {
      const signaturesResponse = await makeRequest('http://localhost:3001/api/signatures', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('Signatures response status:', signaturesResponse.status);
      console.log('Signatures response data:', JSON.stringify(signaturesResponse.data, null, 2));
      
      if (signaturesResponse.status === 200) {
        console.log('✓ Signatures endpoint working correctly');
      } else {
        console.log('❌ Signatures endpoint failed');
      }
    } catch (error) {
      console.log('❌ Signatures request error:', error);
    }

    // Step 5: Test upload endpoint
    console.log('\n5. Testing upload endpoint...');
    try {
      const uploadResponse = await makeRequest('http://localhost:3001/api/documents/upload-for-review', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('Upload response status:', uploadResponse.status);
      console.log('Upload response data:', JSON.stringify(uploadResponse.data, null, 2));
      
      if (uploadResponse.status === 400 && uploadResponse.data.error === 'No file uploaded') {
        console.log('✓ Upload endpoint accessible (correctly expects file)');
      } else {
        console.log('❌ Upload endpoint unexpected response');
      }
    } catch (error) {
      console.log('❌ Upload request error:', error);
    }

    // Step 6: Test CORS headers
    console.log('\n6. Testing CORS headers...');
    try {
      const corsResponse = await makeRequest('http://localhost:3001/health');
      console.log('CORS headers:', corsResponse.headers);
      
      const corsHeaders = [
        'access-control-allow-origin',
        'access-control-allow-methods',
        'access-control-allow-headers'
      ];
      
      const foundCorsHeaders = corsHeaders.filter(header => corsResponse.headers[header]);
      console.log('Found CORS headers:', foundCorsHeaders);
      
      if (foundCorsHeaders.length > 0) {
        console.log('✓ Some CORS headers present');
      } else {
        console.log('⚠ No CORS headers found');
      }
    } catch (error) {
      console.log('❌ CORS test error:', error);
    }

    // Step 7: Test server configuration
    console.log('\n7. Server configuration check...');
    try {
      const healthResponse = await makeRequest('http://localhost:3001/health');
      console.log('Server response headers:', healthResponse.headers);
      console.log('Server response time:', new Date().toISOString());
      
      if (healthResponse.data && healthResponse.data.uptime) {
        console.log('✓ Server uptime:', healthResponse.data.uptime, 'seconds');
      }
      
      if (healthResponse.data && healthResponse.data.memory) {
        console.log('✓ Server memory usage:', healthResponse.data.memory);
      }
    } catch (error) {
      console.log('❌ Server config check error:', error);
    }

    console.log('\n=== Diagnostics Complete ===');
    console.log('Check the detailed output above to identify specific issues.');

  } catch (error) {
    console.error('❌ Diagnostics failed:', error);
  }
}

runAPIdiagnostics();
