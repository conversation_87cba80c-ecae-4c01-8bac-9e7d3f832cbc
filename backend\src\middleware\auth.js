const jwt = require('jsonwebtoken');
const { query } = require('../models/database');

const authenticateToken = async (req, res, next) => {
  try {
    // Test environment bypass
    if (process.env.NODE_ENV === 'test' && req.headers['x-test-auth']) {
      req.user = JSON.parse(req.headers['x-test-auth']);
      return next();
    }

    const authHeader = req.headers['authorization'];
    let token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    // If no token in header, check query parameter (for PDF viewer)
    if (!token && req.query.token) {
      token = req.query.token;
    }

    if (!token) {
      return res.status(401).json({ error: 'Access token required' });
    }

    // Verify the token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Check if user still exists in database
    const userResult = await query(
      'SELECT id, email FROM users WHERE id = $1',
      [decoded.userId]
    );

    if (userResult.rows.length === 0) {
      return res.status(401).json({ error: 'User not found' });
    }

    // Add user info to request object
    req.user = {
      userId: decoded.userId,
      email: userResult.rows[0].email,
      role: 'user' // Default role since column doesn't exist yet
    };

    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(403).json({ error: 'Invalid token' });
    }
    if (error.name === 'TokenExpiredError') {
      return res.status(403).json({ error: 'Token expired' });
    }

    console.error('Auth middleware error:', error);
    return res.status(500).json({ error: 'Authentication error' });
  }
};

const generateToken = (userId, authMethod = 'password') => {
  return jwt.sign(
    {
      userId,
      authMethod,
      iat: Math.floor(Date.now() / 1000)
    },
    process.env.JWT_SECRET,
    { expiresIn: '24h' }
  );
};

const generateRefreshToken = (userId, authMethod = 'password') => {
  return jwt.sign(
    {
      userId,
      type: 'refresh',
      authMethod,
      iat: Math.floor(Date.now() / 1000)
    },
    process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET,
    { expiresIn: '7d' }
  );
};

const verifyRefreshToken = (token) => {
  try {
    const decoded = jwt.verify(token, process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET);
    if (decoded.type !== 'refresh') {
      throw new Error('Invalid token type');
    }
    return decoded;
  } catch (error) {
    throw error;
  }
};

const getTokenExpiration = (token) => {
  try {
    const decoded = jwt.decode(token);
    return decoded.exp * 1000; // Convert to milliseconds
  } catch (error) {
    return null;
  }
};

module.exports = {
  authenticateToken,
  generateToken,
  generateRefreshToken,
  verifyRefreshToken,
  getTokenExpiration
};
