/**
 * Enhanced CORS Configuration
 * Environment-specific CORS settings with advanced security features
 */

const url = require('url');

class CORSConfig {
  constructor() {
    this.allowedOrigins = this.getAllowedOrigins();
    this.trustedDomains = this.getTrustedDomains();
  }

  /**
   * Get allowed origins based on environment
   */
  getAllowedOrigins() {
    const origins = [];

    // Environment-specific origins
    if (process.env.FRONTEND_URL) {
      origins.push(process.env.FRONTEND_URL);
    }

    // Additional allowed origins from environment
    if (process.env.ALLOWED_ORIGINS) {
      const additionalOrigins = process.env.ALLOWED_ORIGINS.split(',')
        .map(origin => origin.trim())
        .filter(origin => origin.length > 0);
      origins.push(...additionalOrigins);
    }

    // Development defaults
    if (process.env.NODE_ENV === 'development') {
      const devOrigins = [
        'http://localhost:3000',
        'https://localhost:3000',
        'http://127.0.0.1:3000',
        'https://127.0.0.1:3000'
      ];
      origins.push(...devOrigins);
    }

    // Production defaults (if no environment variables set)
    if (origins.length === 0 && process.env.NODE_ENV === 'production') {
      console.warn('No CORS origins configured for production environment');
    }

    return [...new Set(origins)]; // Remove duplicates
  }

  /**
   * Get trusted domains for additional validation
   */
  getTrustedDomains() {
    const domains = [];

    if (process.env.TRUSTED_DOMAINS) {
      const trustedDomains = process.env.TRUSTED_DOMAINS.split(',')
        .map(domain => domain.trim().toLowerCase())
        .filter(domain => domain.length > 0);
      domains.push(...trustedDomains);
    }

    // Development trusted domains
    if (process.env.NODE_ENV === 'development') {
      domains.push('localhost', '127.0.0.1');
    }

    return [...new Set(domains)];
  }

  /**
   * Validate origin against security rules
   */
  validateOrigin(origin) {
    if (!origin) {
      // Allow requests with no origin (mobile apps, Postman, etc.) in development
      return process.env.NODE_ENV === 'development';
    }

    try {
      const parsedOrigin = new URL(origin);
      
      // Security checks
      const securityChecks = {
        isHttps: parsedOrigin.protocol === 'https:',
        isLocalhost: ['localhost', '127.0.0.1', '::1'].includes(parsedOrigin.hostname),
        isTrustedDomain: this.trustedDomains.includes(parsedOrigin.hostname.toLowerCase()),
        isAllowedOrigin: this.allowedOrigins.includes(origin),
        hasValidPort: this.isValidPort(parsedOrigin.port),
        hasValidHostname: this.isValidHostname(parsedOrigin.hostname)
      };

      // Log security check results
      console.log('CORS origin validation:', {
        origin,
        hostname: parsedOrigin.hostname,
        protocol: parsedOrigin.protocol,
        port: parsedOrigin.port,
        checks: securityChecks
      });

      // Production security requirements
      if (process.env.NODE_ENV === 'production') {
        // Require HTTPS in production (unless localhost)
        if (!securityChecks.isHttps && !securityChecks.isLocalhost) {
          console.warn(`CORS blocked non-HTTPS origin in production: ${origin}`);
          return false;
        }

        // Must be explicitly allowed in production
        if (!securityChecks.isAllowedOrigin) {
          console.warn(`CORS blocked non-allowed origin in production: ${origin}`);
          return false;
        }
      }

      // Development allows more flexibility
      if (process.env.NODE_ENV === 'development') {
        return securityChecks.isAllowedOrigin || 
               securityChecks.isLocalhost || 
               securityChecks.isTrustedDomain;
      }

      // Default to allowed origins only
      return securityChecks.isAllowedOrigin;

    } catch (error) {
      console.error('CORS origin validation error:', error.message);
      return false;
    }
  }

  /**
   * Validate port number
   */
  isValidPort(port) {
    if (!port) return true; // Default ports are valid
    const portNum = parseInt(port, 10);
    return portNum >= 1 && portNum <= 65535;
  }

  /**
   * Validate hostname
   */
  isValidHostname(hostname) {
    // Basic hostname validation
    const hostnameRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
    
    // Allow localhost and IP addresses
    if (['localhost', '127.0.0.1', '::1'].includes(hostname)) {
      return true;
    }

    // Check for IP addresses
    const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
    if (ipv4Regex.test(hostname)) {
      return hostname.split('.').every(octet => {
        const num = parseInt(octet, 10);
        return num >= 0 && num <= 255;
      });
    }

    return hostnameRegex.test(hostname);
  }

  /**
   * Get CORS configuration object
   */
  getCORSConfig() {
    return {
      origin: (origin, callback) => {
        const isAllowed = this.validateOrigin(origin);
        
        if (isAllowed) {
          callback(null, true);
        } else {
          console.warn(`CORS blocked origin: ${origin || 'null'}`);
          callback(new Error('Not allowed by CORS policy'));
        }
      },
      
      credentials: true,
      
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
      
      allowedHeaders: [
        'Origin',
        'X-Requested-With',
        'Content-Type',
        'Accept',
        'Authorization',
        'X-Test-Auth', // For testing
        'X-API-Key',
        'X-Client-Version',
        'X-Request-ID'
      ],
      
      exposedHeaders: [
        'X-Total-Count',
        'X-Rate-Limit-Remaining',
        'X-Rate-Limit-Reset',
        'X-Request-ID'
      ],
      
      maxAge: process.env.NODE_ENV === 'production' ? 86400 : 300, // 24 hours in prod, 5 minutes in dev
      
      optionsSuccessStatus: 200, // For legacy browser support
      
      preflightContinue: false
    };
  }

  /**
   * Get security headers for CORS responses
   */
  getSecurityHeaders() {
    return {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Cross-Origin-Embedder-Policy': 'require-corp',
      'Cross-Origin-Opener-Policy': 'same-origin',
      'Cross-Origin-Resource-Policy': 'cross-origin'
    };
  }

  /**
   * Middleware to add security headers to CORS responses
   */
  addSecurityHeaders() {
    return (req, res, next) => {
      const securityHeaders = this.getSecurityHeaders();
      
      // Add security headers
      Object.entries(securityHeaders).forEach(([header, value]) => {
        res.setHeader(header, value);
      });

      // Add request ID for tracking
      const requestId = req.headers['x-request-id'] || 
                       require('crypto').randomBytes(16).toString('hex');
      res.setHeader('X-Request-ID', requestId);

      next();
    };
  }

  /**
   * Log CORS activity for monitoring
   */
  logCORSActivity() {
    return (req, res, next) => {
      const origin = req.headers.origin;
      const method = req.method;
      const path = req.path;

      if (origin && method === 'OPTIONS') {
        console.log('CORS preflight request:', {
          origin,
          method,
          path,
          headers: req.headers,
          ip: req.ip,
          userAgent: req.get('User-Agent')
        });
      }

      next();
    };
  }

  /**
   * Get complete CORS middleware stack
   */
  getMiddlewareStack() {
    return [
      this.logCORSActivity(),
      this.addSecurityHeaders(),
      require('cors')(this.getCORSConfig())
    ];
  }
}

// Export singleton instance
module.exports = new CORSConfig();
