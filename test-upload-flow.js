const fs = require('fs');
const http = require('http');
const https = require('https');
const querystring = require('querystring');

// Simple HTTP request helper
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {}
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData, headers: res.headers });
        } catch (e) {
          resolve({ status: res.statusCode, data: data, headers: res.headers });
        }
      });
    });

    req.on('error', reject);
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

// Test complete upload flow
async function testCompleteUploadFlow() {
  console.log('=== Testing Complete Document Upload Flow ===\n');
  
  try {
    // Step 1: Test backend health
    console.log('1. Testing backend health...');
    const healthResponse = await makeRequest('http://localhost:3001/health');
    if (healthResponse.status === 200) {
      console.log('✓ Backend is healthy');
    } else {
      console.log('❌ Backend health check failed');
      return;
    }

    // Step 2: Test user registration/login
    console.log('\n2. Testing authentication...');
    
    // Try to register a test user
    const testEmail = '<EMAIL>';
    const testPassword = 'MySecureP@ssw0rd2024!Complex';
    
    const registerData = JSON.stringify({
      email: testEmail,
      password: testPassword
    });
    
    const registerResponse = await makeRequest('http://localhost:3001/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(registerData)
      },
      body: registerData
    });
    
    let authToken = null;
    
    if (registerResponse.status === 201) {
      console.log('✓ User registered successfully');
      authToken = registerResponse.data.token;
    } else if (registerResponse.status === 409 || registerResponse.data.message.includes('مسجل بالفعل')) {
      console.log('ℹ User already exists, trying login...');
      
      // Try login
      const loginResponse = await makeRequest('http://localhost:3001/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(registerData)
        },
        body: registerData
      });
      
      if (loginResponse.status === 200) {
        console.log('✓ User logged in successfully');
        authToken = loginResponse.data.token;
      } else {
        console.log('❌ Login failed:', loginResponse.data);
        return;
      }
    } else {
      console.log('❌ Registration failed:', registerResponse.data);
      return;
    }

    if (!authToken) {
      console.log('❌ No authentication token obtained');
      return;
    }

    // Step 3: Test authenticated endpoints
    console.log('\n3. Testing authenticated endpoints...');
    
    const profileResponse = await makeRequest('http://localhost:3001/api/auth/profile', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    if (profileResponse.status === 200) {
      console.log('✓ Profile endpoint accessible');
      console.log('  User role:', profileResponse.data.user.role);
    } else {
      console.log('❌ Profile endpoint failed:', profileResponse.data);
    }

    // Step 4: Test document upload endpoints
    console.log('\n4. Testing document upload endpoints...');
    
    // Test upload-for-review endpoint
    const uploadResponse = await makeRequest('http://localhost:3001/api/documents/upload-for-review', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    if (uploadResponse.status === 400) {
      console.log('✓ Upload endpoint accessible (expects file)');
    } else {
      console.log('Upload endpoint response:', uploadResponse.status, uploadResponse.data);
    }

    // Step 5: Test file system permissions
    console.log('\n5. Testing file system...');
    
    const uploadsDir = './backend/uploads';
    const chunksDir = './backend/uploads/chunks';
    
    try {
      if (!fs.existsSync(uploadsDir)) {
        fs.mkdirSync(uploadsDir, { recursive: true });
        console.log('✓ Created uploads directory');
      } else {
        console.log('✓ Uploads directory exists');
      }
      
      if (!fs.existsSync(chunksDir)) {
        fs.mkdirSync(chunksDir, { recursive: true });
        console.log('✓ Created chunks directory');
      } else {
        console.log('✓ Chunks directory exists');
      }
      
      // Test write permissions
      const testFile = './backend/uploads/test-write.txt';
      fs.writeFileSync(testFile, 'test');
      fs.unlinkSync(testFile);
      console.log('✓ Write permissions working');
      
    } catch (error) {
      console.log('❌ File system error:', error.message);
    }

    // Step 6: Check database connectivity
    console.log('\n6. Testing database connectivity...');
    
    // The profile endpoint already tested database connectivity
    console.log('✓ Database connectivity confirmed (profile endpoint worked)');

    // Step 7: Test frontend connectivity
    console.log('\n7. Testing frontend connectivity...');
    
    try {
      const frontendResponse = await makeRequest('http://localhost:3000');
      if (frontendResponse.status === 200) {
        console.log('✓ Frontend is accessible');
      } else {
        console.log('⚠ Frontend returned status:', frontendResponse.status);
      }
    } catch (error) {
      console.log('⚠ Frontend not accessible:', error.message);
      console.log('  (This is expected if frontend is not running)');
    }

    console.log('\n=== Upload Flow Test Summary ===');
    console.log('✓ Backend server running');
    console.log('✓ Authentication working');
    console.log('✓ Database connectivity confirmed');
    console.log('✓ Upload endpoints accessible');
    console.log('✓ File system permissions working');
    console.log('✓ All core upload infrastructure ready');
    
    console.log('\n=== Potential Upload Issues to Check ===');
    console.log('1. Frontend-backend CORS configuration');
    console.log('2. File size limits and timeout settings');
    console.log('3. Multer configuration and memory limits');
    console.log('4. PDF validation logic');
    console.log('5. Error handling and user feedback');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testCompleteUploadFlow();
